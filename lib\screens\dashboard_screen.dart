import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:medical/providers/dashboard_provider.dart';
import 'package:medical/screens/doctor_visits_screen.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/widgets/loading_widget.dart' as widgets;

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DashboardProvider>().loadDashboardData();
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<DashboardProvider>(
        builder: (context, dashboardProvider, child) {
          if (dashboardProvider.isLoading) {
            return const widgets.LoadingWidget(message: 'Loading dashboard...');
          }

          if (dashboardProvider.error != null) {
            return widgets.ErrorWidget(
              message: dashboardProvider.error!,
              onRetry: () => dashboardProvider.loadDashboardData(),
            );
          }

          return RefreshIndicator(
            onRefresh: () => dashboardProvider.loadDashboardData(),
            child: ResponsiveContainer(
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Medical Dashboard',
                      style: TextStyle(
                        fontSize: Responsive.getTitleFontSize(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
              
                    // Statistics Cards
                    _buildStatsGrid(dashboardProvider.stats),

                    const SizedBox(height: 20),

                    // Active Medicines Section
                    Text(
                      'Active Medicines',
                      style: TextStyle(
                        fontSize: Responsive.getSubtitleFontSize(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    dashboardProvider.activeMedicines.isEmpty
                        ? const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Text('No active medicines found.'),
                            ),
                          )
                        : _buildActiveMedicinesList(dashboardProvider.activeMedicines),

                    const SizedBox(height: 20),

                    // Family Members Section
                    Text(
                      'Family Members',
                      style: TextStyle(
                        fontSize: Responsive.getSubtitleFontSize(context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    dashboardProvider.familyMembers.isEmpty
                        ? const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Text('No family members found.'),
                            ),
                          )
                        : _buildFamilyMembersList(dashboardProvider.familyMembers),
                  ],
                ),
              ),
            );
        },
      ),
    );
  }

  Widget _buildStatsGrid(Map<String, int> stats) {
    return ResponsiveGridView(
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('Family Members', stats['familyMembers'] ?? 0, Icons.people, Colors.blue),
        _buildStatCard('Doctor Visits', stats['doctorVisits'] ?? 0, Icons.local_hospital, Colors.green),
        _buildStatCard('Diagnoses', stats['diagnoses'] ?? 0, Icons.medical_services, Colors.orange),
        _buildStatCard('Medical Reports', stats['medicalReports'] ?? 0, Icons.receipt_long, Colors.purple),
      ],
    );
  }

  Widget _buildStatCard(String title, int count, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              count.toString(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveMedicinesList(List<Medicine> medicines) {
    return Card(
      child: Column(
        children: medicines.take(3).map((medicine) {
          final daysLeft = medicine.endDate.difference(DateTime.now()).inDays;
          return ListTile(
            leading: const Icon(Icons.medication, color: Colors.red),
            title: Text(medicine.medicineName),
            subtitle: Text('${medicine.dosage} - ${medicine.frequency}'),
            trailing: Text(
              '$daysLeft days left',
              style: TextStyle(
                color: daysLeft <= 7 ? Colors.red : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFamilyMembersList(List<FamilyMember> members) {
    return Card(
      child: Column(
        children: members.take(3).map((member) {
          return ListTile(
            leading: const Icon(Icons.person, color: Colors.blue),
            title: Text(member.name),
            subtitle: Text(member.relation),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DoctorVisitsScreen(familyMember: member),
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }
}
