import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/doctor_visits_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late Future<List<FamilyMember>> _familyMembers;
  late Future<List<Medicine>> _activeMedicines;
  late Future<Map<String, int>> _stats;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  void _loadDashboardData() {
    setState(() {
      _familyMembers = DatabaseHelper().getFamilyMembers();
      _activeMedicines = _getActiveMedicines();
      _stats = _getStats();
    });
  }

  Future<List<Medicine>> _getActiveMedicines() async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    List<Medicine> allMedicines = [];
    
    for (final member in familyMembers) {
      final visits = await DatabaseHelper().getDoctorVisits(member.id!);
      for (final visit in visits) {
        final medicines = await DatabaseHelper().getMedicines(visit.id!);
        // Filter medicines that are still active (end date is in the future)
        final activeMedicines = medicines.where((medicine) => 
          medicine.endDate.isAfter(DateTime.now())
        ).toList();
        allMedicines.addAll(activeMedicines);
      }
    }
    
    return allMedicines;
  }

  Future<Map<String, int>> _getStats() async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    int totalVisits = 0;
    int totalDiagnoses = 0;
    int totalReports = 0;
    
    for (final member in familyMembers) {
      final visits = await DatabaseHelper().getDoctorVisits(member.id!);
      totalVisits += visits.length;
      
      for (final visit in visits) {
        final diagnoses = await DatabaseHelper().getDiagnoses(visit.id!);
        final reports = await DatabaseHelper().getMedicalReports(visit.id!);
        totalDiagnoses += diagnoses.length;
        totalReports += reports.length;
      }
    }
    
    return {
      'familyMembers': familyMembers.length,
      'doctorVisits': totalVisits,
      'diagnoses': totalDiagnoses,
      'medicalReports': totalReports,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _loadDashboardData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Medical Dashboard',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              
              // Statistics Cards
              FutureBuilder<Map<String, int>>(
                future: _stats,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}');
                  } else if (snapshot.hasData) {
                    final stats = snapshot.data!;
                    return _buildStatsGrid(stats);
                  }
                  return const SizedBox.shrink();
                },
              ),
              
              const SizedBox(height: 20),
              
              // Active Medicines Section
              const Text(
                'Active Medicines',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              FutureBuilder<List<Medicine>>(
                future: _activeMedicines,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}');
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Card(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text('No active medicines found.'),
                      ),
                    );
                  } else {
                    return _buildActiveMedicinesList(snapshot.data!);
                  }
                },
              ),
              
              const SizedBox(height: 20),
              
              // Family Members Section
              const Text(
                'Family Members',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              FutureBuilder<List<FamilyMember>>(
                future: _familyMembers,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}');
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Card(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text('No family members found.'),
                      ),
                    );
                  } else {
                    return _buildFamilyMembersList(snapshot.data!);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsGrid(Map<String, int> stats) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 10,
      mainAxisSpacing: 10,
      children: [
        _buildStatCard('Family Members', stats['familyMembers']!, Icons.people, Colors.blue),
        _buildStatCard('Doctor Visits', stats['doctorVisits']!, Icons.local_hospital, Colors.green),
        _buildStatCard('Diagnoses', stats['diagnoses']!, Icons.medical_services, Colors.orange),
        _buildStatCard('Medical Reports', stats['medicalReports']!, Icons.receipt_long, Colors.purple),
      ],
    );
  }

  Widget _buildStatCard(String title, int count, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              count.toString(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveMedicinesList(List<Medicine> medicines) {
    return Card(
      child: Column(
        children: medicines.take(3).map((medicine) {
          final daysLeft = medicine.endDate.difference(DateTime.now()).inDays;
          return ListTile(
            leading: const Icon(Icons.medication, color: Colors.red),
            title: Text(medicine.medicineName),
            subtitle: Text('${medicine.dosage} - ${medicine.frequency}'),
            trailing: Text(
              '$daysLeft days left',
              style: TextStyle(
                color: daysLeft <= 7 ? Colors.red : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFamilyMembersList(List<FamilyMember> members) {
    return Card(
      child: Column(
        children: members.take(3).map((member) {
          return ListTile(
            leading: const Icon(Icons.person, color: Colors.blue),
            title: Text(member.name),
            subtitle: Text(member.relation),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DoctorVisitsScreen(familyMember: member),
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }
}
