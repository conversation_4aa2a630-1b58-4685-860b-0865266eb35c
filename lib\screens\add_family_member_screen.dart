
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/widgets/date_picker_field.dart';

class AddFamilyMemberScreen extends StatefulWidget {
  final FamilyMember? familyMember;

  const AddFamilyMemberScreen({super.key, this.familyMember});

  @override
  State<AddFamilyMemberScreen> createState() => _AddFamilyMemberScreenState();
}

class _AddFamilyMemberScreenState extends State<AddFamilyMemberScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _dobController = TextEditingController();
  final _relationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.familyMember != null) {
      _nameController.text = widget.familyMember!.name;
      _dobController.text = widget.familyMember!.dateOfBirth;
      _relationController.text = widget.familyMember!.relation;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.familyMember == null ? 'Add Family Member' : 'Edit Family Member'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              DatePickerField(
                controller: _dobController,
                labelText: 'Date of Birth',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a date of birth';
                  }
                  return null;
                },
                lastDate: DateTime.now(),
              ),
              TextFormField(
                controller: _relationController,
                decoration: const InputDecoration(labelText: 'Relation'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a relation';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    if (widget.familyMember == null) {
                      // Add new family member
                      final newMember = FamilyMember(
                        name: _nameController.text,
                        dateOfBirth: _dobController.text,
                        relation: _relationController.text,
                      );
                      await DatabaseHelper().insertFamilyMember(newMember);
                    } else {
                      // Update existing family member
                      final updatedMember = FamilyMember(
                        id: widget.familyMember!.id,
                        name: _nameController.text,
                        dateOfBirth: _dobController.text,
                        relation: _relationController.text,
                      );
                      await DatabaseHelper().updateFamilyMember(updatedMember);
                    }
                    if (mounted) {
                      Navigator.pop(context);
                    }
                  }
                },
                child: Text(widget.familyMember == null ? 'Save' : 'Update'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
