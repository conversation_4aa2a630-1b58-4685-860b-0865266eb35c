import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';

class ExportService {
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Export all data to PDF (placeholder - requires pdf package)
  Future<void> exportToPDF() async {
    throw UnimplementedError('PDF export requires additional packages. Please install pdf and printing packages.');
  }

  // Export data to CSV (simplified version)
  Future<void> exportToCSV() async {
    try {
      final familyMembers = await _databaseHelper.getFamilyMembers();
      StringBuffer csvBuffer = StringBuffer();

      // CSV Headers
      csvBuffer.writeln('Family Member,Date of Birth,Relation,Visit Date,Doctor Name,Reason,Diagnosis,Medicine Name,Dosage,Frequency,Start Date,End Date');

      // Add data rows
      for (final member in familyMembers) {
        final visits = await _databaseHelper.getDoctorVisits(member.id!);

        if (visits.isEmpty) {
          // Add family member without visits
          csvBuffer.writeln('${_escapeCsv(member.name)},${_escapeCsv(member.dateOfBirth)},${_escapeCsv(member.relation)},,,,,,,,,');
        }

        for (final visit in visits) {
          final diagnoses = await _databaseHelper.getDiagnoses(visit.id!);
          final medicines = await _databaseHelper.getMedicines(visit.id!);

          if (diagnoses.isEmpty && medicines.isEmpty) {
            // Add visit without diagnoses or medicines
            csvBuffer.writeln('${_escapeCsv(member.name)},${_escapeCsv(member.dateOfBirth)},${_escapeCsv(member.relation)},${_escapeCsv(visit.visitDate)},${_escapeCsv(visit.doctorName)},${_escapeCsv(visit.reason)},,,,,,,');
          }

          // Add diagnoses
          for (final diagnosis in diagnoses) {
            csvBuffer.writeln('${_escapeCsv(member.name)},${_escapeCsv(member.dateOfBirth)},${_escapeCsv(member.relation)},${_escapeCsv(visit.visitDate)},${_escapeCsv(visit.doctorName)},${_escapeCsv(visit.reason)},${_escapeCsv(diagnosis.diagnosis)},,,,,');
          }

          // Add medicines
          for (final medicine in medicines) {
            csvBuffer.writeln('${_escapeCsv(member.name)},${_escapeCsv(member.dateOfBirth)},${_escapeCsv(member.relation)},${_escapeCsv(visit.visitDate)},${_escapeCsv(visit.doctorName)},${_escapeCsv(visit.reason)},,${_escapeCsv(medicine.medicineName)},${_escapeCsv(medicine.dosage)},${_escapeCsv(medicine.frequency)},${medicine.startDate.toIso8601String().split('T')[0]},${medicine.endDate.toIso8601String().split('T')[0]}');
          }
        }
      }

      // Save CSV
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/medical_records_${DateTime.now().millisecondsSinceEpoch}.csv');
      await file.writeAsString(csvBuffer.toString());

      // For now, just show the file path since we don't have share_plus
      throw Exception('CSV exported to: ${file.path}');
    } catch (e) {
      throw Exception('Failed to export CSV: ${e.toString()}');
    }
  }

  String _escapeCsv(String value) {
    if (value.contains(',') || value.contains('"') || value.contains('\n')) {
      return '"${value.replaceAll('"', '""')}"';
    }
    return value;
  }

  // Print PDF directly (placeholder)
  Future<void> printPDF() async {
    throw UnimplementedError('Print functionality requires additional packages. Please install pdf and printing packages.');
  }
}
