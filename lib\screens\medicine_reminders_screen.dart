import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_reminder_screen.dart';
import 'package:medical/services/notification_service.dart';

class MedicineRemindersScreen extends StatefulWidget {
  final Medicine medicine;

  const MedicineRemindersScreen({super.key, required this.medicine});

  @override
  State<MedicineRemindersScreen> createState() => _MedicineRemindersScreenState();
}

class _MedicineRemindersScreenState extends State<MedicineRemindersScreen> {
  late Future<List<Reminder>> _reminders;

  @override
  void initState() {
    super.initState();
    _loadReminders();
  }

  void _loadReminders() {
    setState(() {
      _reminders = DatabaseHelper().getReminders(widget.medicine.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reminders for ${widget.medicine.medicineName}'),
      ),
      body: Column(
        children: [
          // Medicine Info Card
          Card(
            margin: const EdgeInsets.all(16.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.medicine.medicineName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Dosage: ${widget.medicine.dosage}'),
                  Text('Frequency: ${widget.medicine.frequency}'),
                  Text('Duration: ${_formatDate(widget.medicine.startDate)} - ${_formatDate(widget.medicine.endDate)}'),
                ],
              ),
            ),
          ),
          
          // Reminders List
          Expanded(
            child: FutureBuilder<List<Reminder>>(
              future: _reminders,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.notifications_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No reminders set for this medicine.'),
                      ],
                    ),
                  );
                } else {
                  return ListView.builder(
                    itemCount: snapshot.data!.length,
                    itemBuilder: (context, index) {
                      final reminder = snapshot.data![index];
                      return _buildReminderTile(reminder);
                    },
                  );
                }
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddReminderScreen(medicine: widget.medicine),
            ),
          );
          _loadReminders();
        },
        child: const Icon(Icons.add_alarm),
      ),
    );
  }

  Widget _buildReminderTile(Reminder reminder) {
    final reminderTime = DateTime.parse(reminder.reminderTime);
    final timeString = '${reminderTime.hour.toString().padLeft(2, '0')}:${reminderTime.minute.toString().padLeft(2, '0')}';
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        leading: Icon(
          reminder.enabled ? Icons.alarm_on : Icons.alarm_off,
          color: reminder.enabled ? Colors.green : Colors.grey,
        ),
        title: Text('Reminder at $timeString'),
        subtitle: Text(reminder.enabled ? 'Active' : 'Disabled'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: reminder.enabled,
              onChanged: (value) async {
                final updatedReminder = Reminder(
                  id: reminder.id,
                  medicineId: reminder.medicineId,
                  reminderTime: reminder.reminderTime,
                  enabled: value,
                );
                
                await DatabaseHelper().updateReminder(updatedReminder);
                
                if (value) {
                  // Schedule notification
                  await NotificationService().scheduleMedicineReminder(
                    medicine: widget.medicine,
                    reminder: updatedReminder,
                  );
                } else {
                  // Cancel notification
                  await NotificationService().cancelNotification(reminder.id!);
                }
                
                _loadReminders();
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(reminder),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Reminder reminder) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Reminder'),
          content: const Text('Are you sure you want to delete this reminder?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                await DatabaseHelper().deleteReminder(reminder.id!);
                await NotificationService().cancelNotification(reminder.id!);
                if (mounted) {
                  Navigator.of(context).pop();
                  _loadReminders();
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
