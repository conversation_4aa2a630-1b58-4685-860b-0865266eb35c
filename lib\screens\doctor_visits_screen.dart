
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_doctor_visit_screen.dart';
import 'package:medical/screens/diagnoses_screen.dart';

class DoctorVisitsScreen extends StatefulWidget {
  final FamilyMember familyMember;

  const DoctorVisitsScreen({super.key, required this.familyMember});

  @override
  State<DoctorVisitsScreen> createState() => _DoctorVisitsScreenState();
}

class _DoctorVisitsScreenState extends State<DoctorVisitsScreen> {
  late Future<List<DoctorVisit>> _doctorVisits;

  @override
  void initState() {
    super.initState();
    _loadDoctorVisits();
  }

  void _loadDoctorVisits() {
    setState(() {
      _doctorVisits = DatabaseHelper().getDoctorVisits(widget.familyMember.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.familyMember.name}\'s Doctor Visits'),
      ),
      body: FutureBuilder<List<DoctorVisit>>(
        future: _doctorVisits,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No doctor visits found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final visit = snapshot.data![index];
                return ListTile(
                  title: Text(visit.doctorName),
                  subtitle: Text('${visit.visitDate} - ${visit.reason}'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DiagnosesScreen(visit: visit),
                      ),
                    );
                  },
                );
              },
            );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddDoctorVisitScreen(familyMemberId: widget.familyMember.id!),
            ),
          );
          _loadDoctorVisits();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
