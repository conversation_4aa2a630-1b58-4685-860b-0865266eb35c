
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_doctor_visit_screen.dart';
import 'package:medical/screens/diagnoses_screen.dart';

class DoctorVisitsScreen extends StatefulWidget {
  final FamilyMember familyMember;

  const DoctorVisitsScreen({super.key, required this.familyMember});

  @override
  State<DoctorVisitsScreen> createState() => _DoctorVisitsScreenState();
}

class _DoctorVisitsScreenState extends State<DoctorVisitsScreen> {
  late Future<List<DoctorVisit>> _doctorVisits;
  final TextEditingController _searchController = TextEditingController();
  List<DoctorVisit> _allDoctorVisits = [];
  List<DoctorVisit> _filteredDoctorVisits = [];

  @override
  void initState() {
    super.initState();
    _loadDoctorVisits();
    _searchController.addListener(_filterDoctorVisits);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadDoctorVisits() async {
    final visits = await DatabaseHelper().getDoctorVisits(widget.familyMember.id!);
    setState(() {
      _allDoctorVisits = visits;
      _filteredDoctorVisits = visits;
      _doctorVisits = Future.value(visits);
    });
  }

  void _filterDoctorVisits() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredDoctorVisits = _allDoctorVisits;
      } else {
        _filteredDoctorVisits = _allDoctorVisits.where((visit) {
          return visit.doctorName.toLowerCase().contains(query) ||
                 visit.reason.toLowerCase().contains(query) ||
                 visit.visitDate.toLowerCase().contains(query);
        }).toList();
      }
      _doctorVisits = Future.value(_filteredDoctorVisits);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.familyMember.name}\'s Doctor Visits'),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search doctor visits...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          // Doctor Visits List
          Expanded(
            child: FutureBuilder<List<DoctorVisit>>(
              future: _doctorVisits,
              builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No doctor visits found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final visit = snapshot.data![index];
                return ListTile(
                  title: Text(visit.doctorName),
                  subtitle: Text('${visit.visitDate} - ${visit.reason}'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DiagnosesScreen(visit: visit),
                      ),
                    );
                  },
                );
              },
            );
          }
        },
      ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddDoctorVisitScreen(familyMemberId: widget.familyMember.id!),
            ),
          );
          _loadDoctorVisits();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
