
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

class AddMedicalReportScreen extends StatefulWidget {
  final int visitId;

  const AddMedicalReportScreen({super.key, required this.visitId});

  @override
  State<AddMedicalReportScreen> createState() => _AddMedicalReportScreenState();
}

class _AddMedicalReportScreenState extends State<AddMedicalReportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reportNameController = TextEditingController();
  XFile? _imageFile;

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    setState(() {
      _imageFile = image;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Medical Report'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _reportNameController,
                decoration: const InputDecoration(labelText: 'Report Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a report name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              _imageFile == null
                  ? const Text('No image selected.')
                  : Image.file(File(_imageFile!.path)),
              ElevatedButton(
                onPressed: _pickImage,
                child: const Text('Pick Image'),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate() && _imageFile != null) {
                    final appDir = await getApplicationDocumentsDirectory();
                    final fileName = p.basename(_imageFile!.path);
                    final savedImage = await File(_imageFile!.path).copy('${appDir.path}/$fileName');

                    final newReport = MedicalReport(
                      visitId: widget.visitId,
                      reportName: _reportNameController.text,
                      reportPath: savedImage.path,
                    );
                    await DatabaseHelper().insertMedicalReport(newReport);
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
