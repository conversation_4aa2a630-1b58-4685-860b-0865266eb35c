
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_family_member_screen.dart';
import 'package:medical/screens/doctor_visits_screen.dart';

class FamilyMembersScreen extends StatefulWidget {
  const FamilyMembersScreen({super.key});

  @override
  State<FamilyMembersScreen> createState() => _FamilyMembersScreenState();
}

class _FamilyMembersScreenState extends State<FamilyMembersScreen> {
  late Future<List<FamilyMember>> _familyMembers;
  final TextEditingController _searchController = TextEditingController();
  List<FamilyMember> _allFamilyMembers = [];
  List<FamilyMember> _filteredFamilyMembers = [];

  @override
  void initState() {
    super.initState();
    _loadFamilyMembers();
    _searchController.addListener(_filterFamilyMembers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadFamilyMembers() async {
    final members = await DatabaseHelper().getFamilyMembers();
    setState(() {
      _allFamilyMembers = members;
      _filteredFamilyMembers = members;
      _familyMembers = Future.value(members);
    });
  }

  void _filterFamilyMembers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredFamilyMembers = _allFamilyMembers;
      } else {
        _filteredFamilyMembers = _allFamilyMembers.where((member) {
          return member.name.toLowerCase().contains(query) ||
                 member.relation.toLowerCase().contains(query);
        }).toList();
      }
      _familyMembers = Future.value(_filteredFamilyMembers);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Family Members'),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search family members...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          // Family Members List
          Expanded(
            child: FutureBuilder<List<FamilyMember>>(
              future: _familyMembers,
              builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No family members found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final member = snapshot.data![index];
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                  child: ListTile(
                    title: Text(member.name),
                    subtitle: Text('Relation: ${member.relation}'),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _editFamilyMember(member);
                        } else if (value == 'delete') {
                          _deleteFamilyMember(member);
                        }
                      },
                      itemBuilder: (BuildContext context) => [
                        const PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        const PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DoctorVisitsScreen(familyMember: member),
                        ),
                      );
                    },
                  ),
                );
              },
            );
          }
        },
      ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddFamilyMemberScreen()),
          );
          _loadFamilyMembers();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _editFamilyMember(FamilyMember member) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddFamilyMemberScreen(familyMember: member),
      ),
    );
    _loadFamilyMembers();
  }

  void _deleteFamilyMember(FamilyMember member) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Family Member'),
          content: Text('Are you sure you want to delete ${member.name}? This will also delete all their medical records.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                await DatabaseHelper().deleteFamilyMember(member.id!);
                Navigator.of(context).pop();
                _loadFamilyMembers();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${member.name} deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
