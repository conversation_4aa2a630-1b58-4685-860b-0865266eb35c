
import 'package:medical/models/models.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    String path = join(await getDatabasesPath(), 'medical_records.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE family_members(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        dateOfBirth TEXT NOT NULL,
        relation TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE doctor_visits(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        familyMemberId INTEGER NOT NULL,
        doctorName TEXT NOT NULL,
        visitDate TEXT NOT NULL,
        reason TEXT,
        notes TEXT,
        FOREIGN KEY (familyMemberId) REFERENCES family_members (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE diagnoses(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        diagnosis TEXT NOT NULL,
        date TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE medical_reports(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        reportPath TEXT NOT NULL,
        reportName TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE medicines(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        visitId INTEGER NOT NULL,
        medicineName TEXT NOT NULL,
        dosage TEXT NOT NULL,
        frequency TEXT NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        FOREIGN KEY (visitId) REFERENCES doctor_visits (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE reminders(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medicineId INTEGER NOT NULL,
        reminderTime TEXT NOT NULL,
        enabled INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (medicineId) REFERENCES medicines (id) ON DELETE CASCADE
      )
    ''');
  }

  Future<int> insertFamilyMember(FamilyMember member) async {
    final db = await database;
    return await db.insert('family_members', member.toMap());
  }

  Future<List<FamilyMember>> getFamilyMembers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('family_members');
    return List.generate(maps.length, (i) {
      return FamilyMember.fromMap(maps[i]);
    });
  }

  Future<int> insertDoctorVisit(DoctorVisit visit) async {
    final db = await database;
    return await db.insert('doctor_visits', visit.toMap());
  }

  Future<List<DoctorVisit>> getDoctorVisits(int familyMemberId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'doctor_visits',
      where: 'familyMemberId = ?',
      whereArgs: [familyMemberId],
    );
    return List.generate(maps.length, (i) {
      return DoctorVisit.fromMap(maps[i]);
    });
  }

  Future<int> insertDiagnosis(Diagnosis diagnosis) async {
    final db = await database;
    return await db.insert('diagnoses', diagnosis.toMap());
  }

  Future<List<Diagnosis>> getDiagnoses(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'diagnoses',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return Diagnosis.fromMap(maps[i]);
    });
  }

  Future<int> insertMedicalReport(MedicalReport report) async {
    final db = await database;
    return await db.insert('medical_reports', report.toMap());
  }

  Future<List<MedicalReport>> getMedicalReports(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medical_reports',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return MedicalReport.fromMap(maps[i]);
    });
  }

  Future<int> insertMedicine(Medicine medicine) async {
    final db = await database;
    return await db.insert('medicines', medicine.toMap());
  }

  Future<List<Medicine>> getMedicines(int visitId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'medicines',
      where: 'visitId = ?',
      whereArgs: [visitId],
    );
    return List.generate(maps.length, (i) {
      return Medicine.fromMap(maps[i]);
    });
  }
}
