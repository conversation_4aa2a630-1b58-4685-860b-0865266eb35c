
import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/screens/add_medicine_screen.dart';

class MedicinesScreen extends StatefulWidget {
  final DoctorVisit visit;

  const MedicinesScreen({super.key, required this.visit});

  @override
  State<MedicinesScreen> createState() => _MedicinesScreenState();
}

class _MedicinesScreenState extends State<MedicinesScreen> {
  late Future<List<Medicine>> _medicines;

  @override
  void initState() {
    super.initState();
    _loadMedicines();
  }

  void _loadMedicines() {
    setState(() {
      _medicines = DatabaseHelper().getMedicines(widget.visit.id!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Medicines for ${widget.visit.visitDate}'),
      ),
      body: FutureBuilder<List<Medicine>>(
        future: _medicines,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No medicines found.'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                final medicine = snapshot.data![index];
                return ListTile(
                  title: Text(medicine.medicineName),
                  subtitle: Text('Dosage: ${medicine.dosage} - Frequency: ${medicine.frequency}'),
                );
              },
            );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddMedicineScreen(visitId: widget.visit.id!),
            ),
          );
          _loadMedicines();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
